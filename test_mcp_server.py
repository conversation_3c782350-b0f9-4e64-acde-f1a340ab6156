#!/usr/bin/env python3
"""
Test suite for MCP Search Server
Tests the MCP server functionality using the official MCP Python SDK
"""

import asyncio
import json
import subprocess
import sys
import time
import pytest
from typing import Any, Dict
from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client


class TestMCPServer:
    """Test class for MCP Search Server"""
    
    @pytest.fixture
    async def mcp_client(self):
        """Create an MCP client connected to our server"""
        server_params = StdioServerParameters(
            command=sys.executable,
            args=["mcp_server.py"],
            env=None,
        )
        
        async with stdio_client(server_params) as (read, write):
            async with ClientSession(read, write) as session:
                await session.initialize()
                yield session
    
    async def test_server_initialization(self, mcp_client):
        """Test that the server initializes correctly"""
        # Test that we can list tools
        tools = await mcp_client.list_tools()
        assert len(tools) > 0
        
        # Check that our expected tools are present
        tool_names = [tool.name for tool in tools]
        assert "search_web" in tool_names
        assert "browse_url" in tool_names
    
    async def test_list_resources(self, mcp_client):
        """Test listing resources"""
        resources = await mcp_client.list_resources()
        # Resources might be empty initially, that's ok
        assert isinstance(resources, list)
    
    async def test_list_prompts(self, mcp_client):
        """Test listing prompts"""
        prompts = await mcp_client.list_prompts()
        assert len(prompts) > 0
        
        prompt_names = [prompt.name for prompt in prompts]
        assert "search_prompt" in prompt_names
    
    async def test_search_web_tool(self, mcp_client):
        """Test the search_web tool"""
        # Test with a simple query
        result = await mcp_client.call_tool(
            "search_web", 
            arguments={"query": "python programming", "engine": "bing"}
        )
        
        assert result is not None
        assert "python programming" in result.content[0].text.lower()
    
    async def test_search_web_tool_google(self, mcp_client):
        """Test the search_web tool with Google"""
        result = await mcp_client.call_tool(
            "search_web", 
            arguments={"query": "machine learning", "engine": "google"}
        )
        
        assert result is not None
        assert "machine learning" in result.content[0].text.lower()
    
    async def test_search_web_invalid_engine(self, mcp_client):
        """Test search_web with invalid engine"""
        result = await mcp_client.call_tool(
            "search_web", 
            arguments={"query": "test", "engine": "invalid"}
        )
        
        assert result is not None
        assert "unsupported search engine" in result.content[0].text.lower()
    
    async def test_browse_url_tool(self, mcp_client):
        """Test the browse_url tool"""
        result = await mcp_client.call_tool(
            "browse_url", 
            arguments={"url": "https://httpbin.org/html"}
        )
        
        assert result is not None
        assert "httpbin" in result.content[0].text.lower()
    
    async def test_search_prompt(self, mcp_client):
        """Test the search prompt"""
        result = await mcp_client.get_prompt(
            "search_prompt", 
            arguments={"query": "artificial intelligence"}
        )
        
        assert result is not None
        assert "artificial intelligence" in result.messages[0].content.text
    
    async def test_empty_query(self, mcp_client):
        """Test search with empty query"""
        result = await mcp_client.call_tool(
            "search_web", 
            arguments={"query": ""}
        )
        
        assert result is not None
        assert "no query provided" in result.content[0].text.lower()


class TestMCPServerStandalone:
    """Standalone tests that don't require a full client connection"""
    
    def test_server_starts(self):
        """Test that the server can start without errors"""
        # Start the server process
        process = subprocess.Popen(
            [sys.executable, "mcp_server.py"],
            stdin=subprocess.PIPE,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # Give it a moment to start
        time.sleep(2)
        
        # Check if it's still running (not crashed)
        assert process.poll() is None, "Server process crashed on startup"
        
        # Terminate the process
        process.terminate()
        process.wait(timeout=5)
    
    def test_server_responds_to_initialize(self):
        """Test that server responds to MCP initialize message"""
        process = subprocess.Popen(
            [sys.executable, "mcp_server.py"],
            stdin=subprocess.PIPE,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # Send initialize message
        init_message = {
            "jsonrpc": "2.0",
            "id": 1,
            "method": "initialize",
            "params": {
                "protocolVersion": "2024-11-05",
                "capabilities": {},
                "clientInfo": {
                    "name": "test-client",
                    "version": "1.0.0"
                }
            }
        }
        
        try:
            # Send the message
            process.stdin.write(json.dumps(init_message) + "\n")
            process.stdin.flush()
            
            # Give it time to respond
            time.sleep(1)
            
            # Check if process is still alive
            assert process.poll() is None, "Server crashed after initialize"
            
        finally:
            process.terminate()
            process.wait(timeout=5)


# Async test runner
async def run_async_tests():
    """Run async tests manually"""
    print("Running MCP Server Tests...")
    
    # Test server startup
    standalone_tests = TestMCPServerStandalone()
    
    print("✓ Testing server startup...")
    standalone_tests.test_server_starts()
    
    print("✓ Testing server initialize response...")
    standalone_tests.test_server_responds_to_initialize()
    
    print("All tests passed!")


if __name__ == "__main__":
    # Run the async tests
    asyncio.run(run_async_tests())
