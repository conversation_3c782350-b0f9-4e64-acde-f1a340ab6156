# MCP 服务器故障排除指南

## 🚨 超时错误解决方案

### 问题：`MCP error -2: Request timed out`

这是最常见的问题，通常由以下原因引起：

### 🔧 解决步骤

#### 1. 使用简化版服务器
首先尝试使用简化版服务器，它启动更快：

```json
{
  "mcpServers": {
    "browser_search": {
      "command": "/bin/bash",
      "args": [
        "--noprofile",
        "--norc", 
        "-c",
        "source /home/<USER>/src/browser-use-demo/venv/bin/activate && python /home/<USER>/src/browser-use-demo/mcp_server_simple.py"
      ],
      "timeout": 120
    }
  }
}
```

#### 2. 增加超时时间
将 `timeout` 从 60 增加到 120 或更高：

```json
"timeout": 180
```

#### 3. 添加环境变量
```json
"env": {
  "PYTHONUNBUFFERED": "1",
  "DISPLAY": ":0"
}
```

#### 4. 运行诊断脚本
```bash
cd /home/<USER>/src/browser-use-demo
python diagnose_mcp.py
```

### 🔍 详细诊断

#### 检查依赖
```bash
# 在虚拟环境中检查
source venv/bin/activate
python -c "import mcp, selenium, dotenv; print('All dependencies OK')"
```

#### 检查 Chrome
```bash
# 测试 Chrome 是否可用
python -c "
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
options = Options()
options.add_argument('--headless')
options.add_argument('--no-sandbox')
driver = webdriver.Chrome(options=options)
driver.get('data:text/html,<h1>Test</h1>')
print('Chrome OK:', driver.title)
driver.quit()
"
```

#### 手动测试服务器
```bash
# 启动服务器并查看日志
source venv/bin/activate
python mcp_server_simple.py 2>&1 | tee server.log
```

#### 测试 JSON-RPC 通信
```bash
# 发送初始化消息
echo '{"jsonrpc":"2.0","id":1,"method":"initialize","params":{"protocolVersion":"2024-11-05","capabilities":{},"clientInfo":{"name":"test","version":"1.0"}}}' | python mcp_server_simple.py
```

### 🛠️ 常见问题和解决方案

#### 问题 1: Chrome 启动慢
**症状**: 服务器启动需要很长时间
**解决方案**:
- 使用 `mcp_server_simple.py`（延迟初始化浏览器）
- 增加超时时间到 180 秒
- 检查系统资源（内存、CPU）

#### 问题 2: ChromeDriver 版本不兼容
**症状**: `selenium.common.exceptions.SessionNotCreatedException`
**解决方案**:
```bash
# 更新 ChromeDriver
pip install --upgrade selenium
# 或手动下载匹配的 ChromeDriver
```

#### 问题 3: 网络连接问题
**症状**: 搜索请求失败
**解决方案**:
```bash
# 测试网络连接
curl -I https://www.bing.com
curl -I https://www.google.com
```

#### 问题 4: 权限问题
**症状**: 无法启动 Chrome
**解决方案**:
```bash
# 添加 Chrome 参数
--no-sandbox
--disable-dev-shm-usage
```

### 📊 性能优化

#### 配置优化
```json
{
  "mcpServers": {
    "browser_search": {
      "command": "/bin/bash",
      "args": [
        "--noprofile",
        "--norc",
        "-c", 
        "source /home/<USER>/src/browser-use-demo/venv/bin/activate && python /home/<USER>/src/browser-use-demo/mcp_server_simple.py"
      ],
      "timeout": 120,
      "env": {
        "PYTHONUNBUFFERED": "1",
        "CHROME_FLAGS": "--no-sandbox --disable-dev-shm-usage"
      }
    }
  }
}
```

#### 系统优化
```bash
# 增加共享内存
sudo mount -o remount,size=2G /dev/shm

# 检查可用内存
free -h

# 检查 Chrome 进程
ps aux | grep chrome
```

### 🔄 测试流程

#### 1. 基础测试
```bash
python diagnose_mcp.py
```

#### 2. 简单连接测试
使用 `test_connection` 工具测试基本功能

#### 3. 搜索测试
使用简单查询测试搜索功能

#### 4. 逐步调试
如果仍有问题，按以下顺序检查：
1. 依赖安装
2. Chrome 可用性
3. 网络连接
4. 服务器启动
5. MCP 通信

### 📝 日志分析

#### 启用详细日志
```bash
# 在 .env 文件中添加
DEBUG=1
PYTHONUNBUFFERED=1
```

#### 查看日志
```bash
# 服务器日志
tail -f /tmp/mcp_debug.log

# 系统日志
journalctl -f | grep chrome
```

### 🆘 如果仍然无法解决

1. **收集信息**:
   - 运行 `diagnose_mcp.py` 的完整输出
   - 服务器启动日志
   - 系统信息 (`uname -a`, `python --version`)

2. **尝试最小配置**:
   ```json
   {
     "mcpServers": {
       "test": {
         "command": "python",
         "args": ["/home/<USER>/src/browser-use-demo/mcp_server_simple.py"],
         "timeout": 300
       }
     }
   }
   ```

3. **检查替代方案**:
   - 使用无浏览器的搜索 API
   - 使用预配置的 Docker 容器
   - 在不同的机器上测试

### 📞 获取帮助

如果问题持续存在，请提供：
- `diagnose_mcp.py` 的输出
- 服务器启动日志
- 系统配置信息
- 错误的完整堆栈跟踪
