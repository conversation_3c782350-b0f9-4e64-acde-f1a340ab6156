#!/usr/bin/env python3
"""
简化版 MCP 搜索服务器
用于快速测试和调试，减少启动时间
"""

import asyncio
import os
import logging
import time
from typing import Optional
from dotenv import load_dotenv
from contextlib import asynccontextmanager
from typing import AsyncIterator
from dataclasses import dataclass

# Import official MCP SDK
from mcp.server.fastmcp import FastMCP

load_dotenv()

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Configuration
CUSTOM_CHROME = os.getenv('CUSTOM_CHROME')
CUSTOM_CHROME_DRIVER = os.getenv('CUSTOM_CHROME_DRIVER')

@dataclass
class AppContext:
    browser: Optional[object] = None  # 延迟初始化

@asynccontextmanager
async def app_lifespan(server: FastMCP) -> AsyncIterator[AppContext]:
    """轻量级生命周期管理，延迟初始化浏览器"""
    logger.info("🚀 Starting Simple MCP Search Server...")
    
    # 不在启动时初始化浏览器，而是在需要时初始化
    context = AppContext()
    
    try:
        yield context
    finally:
        # 清理浏览器
        if context.browser:
            try:
                context.browser.quit()
                logger.info("✅ Browser cleanup completed")
            except Exception as e:
                logger.error(f"⚠️ Error during cleanup: {e}")

def init_browser_if_needed(context: AppContext):
    """按需初始化浏览器"""
    if context.browser is not None:
        return context.browser
    
    logger.info("🌐 Initializing Chrome browser on demand...")
    
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        from selenium.webdriver.chrome.service import Service as ChromeService
        
        chrome_options = Options()
        chrome_options.add_argument('--headless=new')
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--disable-extensions')
        chrome_options.add_argument('--disable-background-timer-throttling')
        chrome_options.add_argument('--disable-backgrounding-occluded-windows')
        chrome_options.add_argument('--disable-renderer-backgrounding')
        chrome_options.add_argument('--disable-features=TranslateUI')
        chrome_options.add_argument('--window-size=1280,720')
        
        if CUSTOM_CHROME:
            chrome_options.binary_location = CUSTOM_CHROME
        
        if CUSTOM_CHROME_DRIVER:
            service = ChromeService(executable_path=CUSTOM_CHROME_DRIVER)
        else:
            service = ChromeService()
        
        browser = webdriver.Chrome(service=service, options=chrome_options)
        context.browser = browser
        
        logger.info("✅ Chrome browser initialized successfully")
        return browser
        
    except Exception as e:
        logger.error(f"❌ Failed to initialize browser: {e}")
        raise

# Create MCP server with lifespan management
mcp = FastMCP("Simple Search Server", lifespan=app_lifespan)

@mcp.tool()
async def search_web(query: str, engine: str = "bing") -> str:
    """
    Search the web using the specified search engine.
    
    Args:
        query: The search query to execute
        engine: The search engine to use (bing or google)
    
    Returns:
        Search results including page title and content
    """
    if not query:
        return "Error: No query provided"
    
    logger.info(f"🔍 Starting search: '{query}' on {engine}")
    start_time = time.time()
    
    try:
        # Get context and initialize browser if needed
        ctx = mcp.get_context()
        app_context = ctx.request_context.lifespan_context
        browser = init_browser_if_needed(app_context)
        
        # Set timeouts
        browser.set_page_load_timeout(20)
        browser.implicitly_wait(5)
        
        # Build search URL
        if engine.lower() == "bing":
            search_url = f"https://www.bing.com/search?q={query}"
        elif engine.lower() == "google":
            search_url = f"https://www.google.com/search?q={query}"
        else:
            return f"Error: Unsupported search engine: {engine}. Use 'bing' or 'google'."
        
        logger.info(f"🌐 Navigating to: {search_url}")
        browser.get(search_url)
        
        # Wait a bit for page to load
        await asyncio.sleep(2)
        
        # Extract basic information
        title = browser.title
        current_url = browser.current_url
        
        # Try to extract search results
        try:
            from selenium.webdriver.common.by import By
            
            if engine.lower() == "bing":
                # Bing search results
                results = browser.find_elements(By.CSS_SELECTOR, ".b_algo h2")
                result_texts = [elem.text for elem in results[:3] if elem.text.strip()]
            elif engine.lower() == "google":
                # Google search results
                results = browser.find_elements(By.CSS_SELECTOR, "h3")
                result_texts = [elem.text for elem in results[:3] if elem.text.strip()]
            else:
                result_texts = []
            
            if result_texts:
                results_summary = "\n".join([f"• {text}" for text in result_texts])
            else:
                results_summary = "No specific results extracted"
                
        except Exception as e:
            logger.warning(f"⚠️ Could not extract specific results: {e}")
            results_summary = "Results extraction failed, but page loaded successfully"
        
        elapsed_time = time.time() - start_time
        logger.info(f"✅ Search completed in {elapsed_time:.2f} seconds")
        
        return f"""🔍 Search Results

Query: {query}
Engine: {engine}
Time: {elapsed_time:.2f}s
Status: ✅ Success

📋 Top Results:
{results_summary}

🌐 Page Info:
Title: {title}
URL: {current_url}
"""
        
    except Exception as e:
        elapsed_time = time.time() - start_time
        error_msg = f"❌ Error after {elapsed_time:.2f}s: {str(e)}"
        logger.error(error_msg)
        return error_msg

@mcp.tool()
async def test_connection() -> str:
    """
    Test the MCP server connection and browser functionality.
    
    Returns:
        Connection test results
    """
    logger.info("🧪 Running connection test...")
    start_time = time.time()
    
    try:
        # Test 1: Basic server response
        test_results = ["✅ MCP server responding"]
        
        # Test 2: Browser initialization
        ctx = mcp.get_context()
        app_context = ctx.request_context.lifespan_context
        browser = init_browser_if_needed(app_context)
        test_results.append("✅ Browser initialized")
        
        # Test 3: Simple navigation
        browser.get("data:text/html,<html><body><h1>Test Page</h1></body></html>")
        title = browser.title
        test_results.append(f"✅ Navigation test: {title}")
        
        elapsed_time = time.time() - start_time
        
        return f"""🧪 Connection Test Results

Time: {elapsed_time:.2f}s

Tests:
{chr(10).join(test_results)}

✅ All tests passed! Server is ready for use.
"""
        
    except Exception as e:
        elapsed_time = time.time() - start_time
        error_msg = f"❌ Test failed after {elapsed_time:.2f}s: {str(e)}"
        logger.error(error_msg)
        return error_msg

@mcp.resource("search://results/{query}")
def get_search_results(query: str) -> str:
    """Get cached search results for a query"""
    return f"Search results resource for query: {query}"

@mcp.prompt()
def search_prompt(query: str) -> str:
    """Create a search prompt template"""
    return f"Please search for information about: {query}"

if __name__ == "__main__":
    logger.info("🚀 Starting Simple MCP Search Server with stdio protocol...")
    mcp.run()
