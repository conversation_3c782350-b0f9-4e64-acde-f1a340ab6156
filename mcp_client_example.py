#!/usr/bin/env python3
"""
MCP Client Example
演示如何使用官方 MCP Python SDK 连接到搜索服务器
"""

import asyncio
import sys
from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client


async def test_search_server():
    """测试搜索服务器的完整功能"""
    
    # 配置服务器参数
    server_params = StdioServerParameters(
        command=sys.executable,  # Python 解释器
        args=["mcp_server.py"],  # 服务器脚本
        env=None,  # 环境变量
    )
    
    print("🚀 连接到 MCP 搜索服务器...")
    
    try:
        # 建立连接
        async with stdio_client(server_params) as (read, write):
            async with ClientSession(read, write) as session:
                # 初始化连接
                print("📡 初始化 MCP 连接...")
                await session.initialize()
                print("✅ 连接成功!")
                
                # 测试 1: 列出可用工具
                print("\n🔧 列出可用工具...")
                tools = await session.list_tools()
                print(f"找到 {len(tools)} 个工具:")
                for tool in tools:
                    print(f"  - {tool.name}: {tool.description}")
                
                # 测试 2: 列出可用资源
                print("\n📚 列出可用资源...")
                resources = await session.list_resources()
                print(f"找到 {len(resources)} 个资源:")
                for resource in resources:
                    print(f"  - {resource.uri}: {resource.name}")
                
                # 测试 3: 列出可用提示
                print("\n💬 列出可用提示...")
                prompts = await session.list_prompts()
                print(f"找到 {len(prompts)} 个提示:")
                for prompt in prompts:
                    print(f"  - {prompt.name}: {prompt.description}")
                
                # 测试 4: 执行搜索工具
                print("\n🔍 测试搜索功能...")
                search_result = await session.call_tool(
                    "search_web",
                    arguments={
                        "query": "Python MCP protocol",
                        "engine": "bing"
                    }
                )
                
                if search_result.content:
                    content = search_result.content[0].text
                    print(f"搜索结果预览: {content[:200]}...")
                else:
                    print("❌ 搜索返回空结果")
                
                # 测试 5: 测试 URL 浏览
                print("\n🌐 测试 URL 浏览功能...")
                browse_result = await session.call_tool(
                    "browse_url",
                    arguments={
                        "url": "https://httpbin.org/html"
                    }
                )
                
                if browse_result.content:
                    content = browse_result.content[0].text
                    print(f"浏览结果预览: {content[:200]}...")
                else:
                    print("❌ 浏览返回空结果")
                
                # 测试 6: 测试提示功能
                print("\n💭 测试提示功能...")
                prompt_result = await session.get_prompt(
                    "search_prompt",
                    arguments={
                        "query": "机器学习算法"
                    }
                )
                
                if prompt_result.messages:
                    message = prompt_result.messages[0]
                    print(f"提示内容: {message.content.text}")
                else:
                    print("❌ 提示返回空结果")
                
                # 测试 7: 错误处理
                print("\n⚠️  测试错误处理...")
                try:
                    error_result = await session.call_tool(
                        "search_web",
                        arguments={
                            "query": "",  # 空查询
                            "engine": "bing"
                        }
                    )
                    if error_result.content:
                        content = error_result.content[0].text
                        if "error" in content.lower():
                            print("✅ 错误处理正常")
                        else:
                            print("⚠️  未检测到错误处理")
                except Exception as e:
                    print(f"✅ 捕获到预期错误: {e}")
                
                print("\n🎉 所有测试完成!")
                
    except Exception as e:
        print(f"❌ 连接或测试失败: {e}")
        import traceback
        traceback.print_exc()


async def interactive_client():
    """交互式客户端，允许用户手动测试功能"""
    
    server_params = StdioServerParameters(
        command=sys.executable,
        args=["mcp_server.py"],
        env=None,
    )
    
    print("🎮 MCP 搜索服务器交互式客户端")
    print("=" * 40)
    
    try:
        async with stdio_client(server_params) as (read, write):
            async with ClientSession(read, write) as session:
                await session.initialize()
                print("✅ 已连接到服务器")
                
                while True:
                    print("\n可用命令:")
                    print("1. search - 搜索网页")
                    print("2. browse - 浏览 URL")
                    print("3. tools - 列出工具")
                    print("4. resources - 列出资源")
                    print("5. prompts - 列出提示")
                    print("6. quit - 退出")
                    
                    choice = input("\n请选择命令 (1-6): ").strip()
                    
                    if choice == "1":
                        query = input("请输入搜索查询: ").strip()
                        engine = input("请选择搜索引擎 (bing/google) [bing]: ").strip() or "bing"
                        
                        if query:
                            print("🔍 搜索中...")
                            result = await session.call_tool(
                                "search_web",
                                arguments={"query": query, "engine": engine}
                            )
                            if result.content:
                                print(f"\n结果:\n{result.content[0].text}")
                            else:
                                print("❌ 无结果")
                        else:
                            print("❌ 查询不能为空")
                    
                    elif choice == "2":
                        url = input("请输入 URL: ").strip()
                        
                        if url:
                            print("🌐 浏览中...")
                            result = await session.call_tool(
                                "browse_url",
                                arguments={"url": url}
                            )
                            if result.content:
                                print(f"\n结果:\n{result.content[0].text}")
                            else:
                                print("❌ 无结果")
                        else:
                            print("❌ URL 不能为空")
                    
                    elif choice == "3":
                        tools = await session.list_tools()
                        print(f"\n可用工具 ({len(tools)}):")
                        for tool in tools:
                            print(f"  - {tool.name}: {tool.description}")
                    
                    elif choice == "4":
                        resources = await session.list_resources()
                        print(f"\n可用资源 ({len(resources)}):")
                        for resource in resources:
                            print(f"  - {resource.uri}: {resource.name}")
                    
                    elif choice == "5":
                        prompts = await session.list_prompts()
                        print(f"\n可用提示 ({len(prompts)}):")
                        for prompt in prompts:
                            print(f"  - {prompt.name}: {prompt.description}")
                    
                    elif choice == "6":
                        print("👋 再见!")
                        break
                    
                    else:
                        print("❌ 无效选择")
                
    except KeyboardInterrupt:
        print("\n👋 用户中断，退出")
    except Exception as e:
        print(f"❌ 错误: {e}")


async def main():
    """主函数"""
    if len(sys.argv) > 1 and sys.argv[1] == "--interactive":
        await interactive_client()
    else:
        await test_search_server()


if __name__ == "__main__":
    print("MCP 搜索服务器客户端示例")
    print("使用 --interactive 参数启动交互模式")
    print()
    
    asyncio.run(main())
