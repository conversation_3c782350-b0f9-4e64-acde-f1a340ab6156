# MCP 搜索服务器快速开始

## 🚀 一键安装和测试

```bash
# 1. 安装依赖并运行测试
python setup_and_test.py

# 2. 或者手动安装
pip install mcp selenium python-dotenv

# 3. 运行服务器
python mcp_server.py
```

## 📋 文件说明

### 核心文件
- `mcp_server.py` - 主服务器文件（使用官方 MCP SDK + stdio 协议）
- `requirements.txt` - 依赖列表

### 测试文件
- `simple_test.py` - 基本功能测试
- `test_mcp_server.py` - 完整测试套件（需要 pytest）
- `mcp_client_example.py` - 客户端使用示例

### 工具文件
- `setup_and_test.py` - 自动安装和测试脚本
- `README.md` - 详细文档
- `QUICKSTART.md` - 本文件

## 🔧 主要变更（从旧版本）

### 协议变更
- **旧版**: HTTP/SSE 协议 (fastmcp 库)
- **新版**: stdio 协议 (官方 MCP SDK)

### 代码对比

**旧版本**:
```python
from fastmcp import MCPServer, MCPRequest, MCPResponse

class SearchMCPServer(MCPServer):
    def __init__(self, host="0.0.0.0", port=8000):
        super().__init__(host=host, port=port)
    
    async def handle_search(self, request: MCPRequest):
        # HTTP 处理逻辑
        pass

server = SearchMCPServer()
server.start()  # 启动 HTTP 服务器
```

**新版本**:
```python
from mcp.server.fastmcp import FastMCP

mcp = FastMCP("Search Server", lifespan=app_lifespan)

@mcp.tool()
async def search_web(query: str, engine: str = "bing") -> str:
    # stdio 协议，自动处理
    pass

mcp.run()  # 启动 stdio 服务器
```

## 🧪 测试方法

### 1. 基本测试
```bash
python simple_test.py
```

### 2. 客户端测试
```bash
# 自动测试
python mcp_client_example.py

# 交互式测试
python mcp_client_example.py --interactive
```

### 3. 在 Claude Desktop 中测试

配置文件位置：
- **Windows**: `%APPDATA%\Claude\claude_desktop_config.json`
- **macOS**: `~/Library/Application Support/Claude/claude_desktop_config.json`
- **Linux**: `~/.config/Claude/claude_desktop_config.json`

配置内容：
```json
{
  "mcpServers": {
    "search-server": {
      "command": "python",
      "args": ["C:/path/to/mcp_server.py"],
      "env": {}
    }
  }
}
```

## 🛠️ 可用工具

### `search_web`
搜索网页内容
```json
{
  "query": "Python programming",
  "engine": "bing"  // 可选: "bing" 或 "google"
}
```

### `browse_url`
浏览指定网页
```json
{
  "url": "https://example.com"
}
```

## ❓ 常见问题

### Q: 服务器启动失败
A: 检查依赖安装：`pip install mcp selenium python-dotenv`

### Q: Chrome 相关错误
A: 确保安装了 Chrome 浏览器和 ChromeDriver

### Q: 搜索超时
A: 检查网络连接，某些网站可能有反爬虫机制

### Q: MCP 连接失败
A: 确保使用 stdio 协议，检查客户端配置

## 🔍 调试技巧

1. **查看服务器日志**:
   ```bash
   python mcp_server.py 2>&1 | tee server.log
   ```

2. **测试 JSON-RPC 消息**:
   ```bash
   echo '{"jsonrpc":"2.0","id":1,"method":"initialize","params":{"protocolVersion":"2024-11-05","capabilities":{},"clientInfo":{"name":"test","version":"1.0"}}}' | python mcp_server.py
   ```

3. **检查依赖**:
   ```python
   import mcp, selenium, dotenv
   print("所有依赖已安装")
   ```

## 📚 更多信息

- 详细文档: `README.md`
- MCP 官方文档: https://modelcontextprotocol.io
- MCP Python SDK: https://github.com/modelcontextprotocol/python-sdk
