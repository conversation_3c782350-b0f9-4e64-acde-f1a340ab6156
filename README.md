# MCP Search Server

一个基于官方 MCP (Model Context Protocol) Python SDK 的搜索服务器，使用 stdio 协议实现。

## 功能特性

- ✅ 使用官方 MCP Python SDK
- ✅ stdio 协议通信（标准输入/输出）
- ✅ 网页搜索功能（支持 Bing 和 Google）
- ✅ URL 浏览功能
- ✅ 自动浏览器管理（启动和清理）
- ✅ 完整的测试套件

## 安装依赖

```bash
# 安装官方 MCP SDK
pip install mcp

# 安装其他依赖
pip install selenium python-dotenv

# 或者一次性安装所有依赖
pip install -r requirements.txt
```

## 配置

创建 `.env` 文件（可选）：

```env
# 自定义 Chrome 浏览器路径（可选）
CUSTOM_CHROME=/path/to/chrome

# 自定义 ChromeDriver 路径（可选）
CUSTOM_CHROME_DRIVER=/path/to/chromedriver
```

## 使用方法

### 1. 直接运行服务器

```bash
python mcp_server.py
```

服务器将启动并等待 stdio 输入。

### 2. 使用 MCP 客户端连接

#### 在 Claude Desktop 中配置

在 Claude Desktop 的配置文件中添加：

```json
{
  "mcpServers": {
    "search-server": {
      "command": "python",
      "args": ["path/to/mcp_server.py"],
      "env": {}
    }
  }
}
```

#### 使用 MCP Inspector 测试

```bash
# 如果安装了 MCP CLI 工具
mcp dev mcp_server.py
```

### 3. 可用的工具

#### `search_web`
搜索网页内容

参数：
- `query` (string): 搜索查询
- `engine` (string, 可选): 搜索引擎，默认 "bing"，支持 "bing" 或 "google"

示例：
```json
{
  "tool": "search_web",
  "arguments": {
    "query": "Python programming",
    "engine": "bing"
  }
}
```

#### `browse_url`
浏览指定 URL

参数：
- `url` (string): 要访问的 URL

示例：
```json
{
  "tool": "browse_url",
  "arguments": {
    "url": "https://example.com"
  }
}
```

### 4. 可用的资源

#### `search://results/{query}`
获取搜索结果资源

### 5. 可用的提示

#### `search_prompt`
创建搜索提示模板

参数：
- `query` (string): 搜索查询

## 测试

### 运行基本测试

```bash
python simple_test.py
```

这将测试：
- 服务器启动
- MCP 协议初始化
- 基本通信

### 运行完整测试套件

```bash
# 如果安装了 pytest
pytest test_mcp_server.py

# 或者直接运行
python test_mcp_server.py
```

## 故障排除

### 常见问题

1. **服务器启动失败**
   - 检查是否安装了所有依赖
   - 确保 Chrome 浏览器已安装
   - 检查 ChromeDriver 是否可用

2. **搜索超时**
   - 检查网络连接
   - 某些网站可能有反爬虫机制
   - 尝试增加等待时间

3. **MCP 连接问题**
   - 确保使用 stdio 协议
   - 检查客户端配置
   - 查看服务器日志输出

### 调试模式

在代码中添加调试输出：

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 架构说明

### MCP 协议
- 使用官方 MCP Python SDK
- stdio 传输协议
- JSON-RPC 2.0 消息格式

### 浏览器管理
- 使用 Selenium WebDriver
- 自动启动和清理 Chrome 浏览器
- 支持无头模式运行

### 生命周期管理
- 使用 FastMCP 的 lifespan 管理
- 服务器启动时初始化浏览器
- 服务器关闭时自动清理资源

## 与旧版本的区别

### 从 fastmcp 迁移到官方 MCP SDK

**旧版本 (fastmcp)**:
```python
from fastmcp import MCPServer, MCPRequest, MCPResponse

class SearchMCPServer(MCPServer):
    def __init__(self, host="0.0.0.0", port=8000):
        super().__init__(host=host, port=port)
    
    async def handle_search(self, request: MCPRequest) -> MCPResponse:
        # HTTP/SSE 协议处理
        pass
```

**新版本 (官方 SDK)**:
```python
from mcp.server.fastmcp import FastMCP

mcp = FastMCP("Search Server", lifespan=app_lifespan)

@mcp.tool()
async def search_web(query: str, engine: str = "bing") -> str:
    # stdio 协议，自动处理
    pass
```

### 主要改进

1. **协议变更**: HTTP/SSE → stdio
2. **更好的类型支持**: 完整的类型注解
3. **生命周期管理**: 自动资源管理
4. **标准化**: 符合官方 MCP 规范
5. **更简单的 API**: 装饰器风格的工具定义

## 许可证

MIT License
