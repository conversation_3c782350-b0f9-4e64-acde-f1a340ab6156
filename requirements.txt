# MCP 搜索服务器核心依赖
mcp
selenium
python-dotenv

# 可选依赖（用于测试）
pytest
pytest-asyncio

# 原有依赖（如果需要其他功能）
aiofiles==24.1.0
aiohappyeyeballs==2.6.1
aiohttp==3.11.18
aiosignal==1.3.2
annotated-types==0.7.0
anthropic==0.50.0
anyio==4.9.0
attrs==25.3.0
babel==2.17.0
backoff==2.2.1
beautifulsoup4==4.13.4
botocore==1.38.4
browser-use==0.1.6
certifi==2025.4.26
charset-normalizer==3.4.1
click==8.1.8
courlan==1.3.2
dateparser==1.2.1
distro==1.9.0
fastapi==0.115.12
ffmpy==0.5.0
filelock==3.18.0
fireworks-ai==0.15.12
frozenlist==1.6.0
fsspec==2025.3.2
gradio==5.27.0
gradio_client==1.9.0
groovy==0.1.2
h11==0.16.0
html2text==2025.4.15
htmldate==1.9.3
httpcore==1.0.9
httpx==0.28.1
httpx-sse==0.4.0
httpx-ws==0.7.2
huggingface-hub==0.30.2
idna==3.10
Jinja2==3.1.6
jiter==0.9.0
jmespath==1.0.1
jsonpatch==1.33
jsonpointer==3.0.0
jusText==3.0.2
langchain==0.3.24
langchain-anthropic==0.3.12
langchain-core==0.3.56
langchain-fireworks==0.3.0
langchain-openai==0.3.14
langchain-text-splitters==0.3.8
langsmith==0.3.37
lxml==5.4.0
lxml_html_clean==0.4.2
MainContentExtractor==0.0.4
markdown-it-py==3.0.0
MarkupSafe==3.0.2
mdurl==0.1.2
monotonic==1.6
multidict==6.4.3
numpy==2.2.5
openai==1.76.0
orjson==3.10.16
outcome==1.3.0.post0
packaging==24.2
pandas==2.2.3
pillow==11.2.1
posthog==4.0.0
propcache==0.3.1
pydantic==2.11.3
pydantic_core==2.33.1
pydub==0.25.1
Pygments==2.19.1
PySocks==1.7.1
python-dateutil==2.9.0.post0
python-dotenv==1.1.0
python-multipart==0.0.20
pytz==2025.2
PyYAML==6.0.2
regex==2024.11.6
requests==2.32.3
requests-toolbelt==1.0.0
rich==14.0.0
ruff==0.11.7
safehttpx==0.1.6
selenium==4.31.0
Selenium-Screenshot==2.1.0
semantic-version==2.10.0
shellingham==1.5.4
six==1.17.0
sniffio==1.3.1
sortedcontainers==2.4.0
soupsieve==2.7
SQLAlchemy==2.0.40
starlette==0.46.2
tenacity==9.1.2
tiktoken==0.9.0
tld==0.13
tomlkit==0.13.2
tqdm==4.67.1
trafilatura==2.0.0
trio==0.30.0
trio-websocket==0.12.2
typer==0.15.2
typing-inspection==0.4.0
typing_extensions==4.13.2
tzdata==2025.2
tzlocal==5.3.1
urllib3==2.4.0
uvicorn==0.34.2
webdriver-manager==4.0.2
websocket-client==1.8.0
websockets==15.0.1
wsproto==1.2.0
yarl==1.20.0
zstandard==0.23.0
fastmcp==2.6.0
