#!/usr/bin/env python3
"""
Search MCP Server
Provides web search capabilities through MCP protocol using stdio
"""

import asyncio
import os
from typing import Optional
from dotenv import load_dotenv
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service as ChromeService
from contextlib import asynccontextmanager
from typing import AsyncIterator
from dataclasses import dataclass

# Import browser-use framework
from browser_use import Browser
from browser_use.browser.views import TabInfo
from browser_use.controller.service import Controller
from Screenshot import Screenshot
from browser_use.controller.registry.service import Registry
from browser_use.controller.views import (
    ClickElementAction,
    DoneAction,
    ExtractPageContentAction,
    GoToUrlAction,
    InputTextAction,
    OpenTabAction,
    ScrollDownAction,
    SearchGoogleAction,
    SwitchTabAction,
)

# Import official MCP SDK
from mcp.server.fastmcp import FastMCP, Client
from fastmcp.client import Client
import asyncio

load_dotenv()

# Configuration
CUSTOM_CHROME = os.getenv('CUSTOM_CHROME')
CUSTOM_CHROME_DRIVER = os.getenv('CUSTOM_CHROME_DRIVER')

class RVBrowser(Browser):
    def __init__(self, headless: bool = True, keep_open: bool = False):
        self.headless = headless
        self.keep_open = keep_open
        self.MINIMUM_WAIT_TIME = 0.5
        self.MAXIMUM_WAIT_TIME = 5
        self._tab_cache: dict[str, TabInfo] = {}
        self._current_handle = None
        self._ob = Screenshot.Screenshot()
        # Initialize driver during construction
        self.driver: webdriver.Chrome | None = self._setup_webdriver()
        self._cached_state = Browser._update_state(self)

    def _setup_webdriver(self) -> webdriver.Chrome:
        """Sets up and returns a Selenium WebDriver instance with anti-detection measures."""
        import logging
        import tempfile
        import shutil
        import subprocess

        logger = logging.getLogger(__name__)

        try:
            # Check if we're in MCP environment (no DISPLAY, limited environment)
            is_mcp_env = not os.environ.get('DISPLAY') and not os.environ.get('WAYLAND_DISPLAY')
            logger.info(f"🔍 Environment check - MCP mode: {is_mcp_env}")

            chrome_options = Options()

            # Force headless mode, especially for MCP
            chrome_options.add_argument('--headless=new')
            logger.info("🔧 Using headless mode")

            # Essential automation and performance settings
            chrome_options.add_argument('--disable-blink-features=AutomationControlled')
            chrome_options.add_experimental_option('excludeSwitches', ['enable-automation'])
            chrome_options.add_experimental_option('useAutomationExtension', False)

            # CRITICAL: Most aggressive Chrome options for MCP environment
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--disable-gpu-sandbox')
            chrome_options.add_argument('--disable-software-rasterizer')
            chrome_options.add_argument('--disable-features=VizDisplayCompositor')

            # Additional critical options for server environments
            chrome_options.add_argument('--single-process')  # 单进程模式
            chrome_options.add_argument('--no-zygote')  # 禁用zygote进程
            chrome_options.add_argument('--disable-setuid-sandbox')  # 禁用setuid沙箱
            chrome_options.add_argument('--disable-namespace-sandbox')  # 禁用命名空间沙箱

            # Display and window management
            chrome_options.add_argument('--window-size=1280,1024')
            chrome_options.add_argument('--disable-extensions')
            chrome_options.add_argument('--disable-plugins')
            chrome_options.add_argument('--virtual-time-budget=5000')  # 虚拟时间预算

            # Security and permissions (relaxed for MCP environment)
            chrome_options.add_argument('--disable-web-security')
            chrome_options.add_argument('--allow-running-insecure-content')
            chrome_options.add_argument('--ignore-certificate-errors')
            chrome_options.add_argument('--ignore-ssl-errors')
            chrome_options.add_argument('--ignore-certificate-errors-spki-list')
            chrome_options.add_argument('--disable-features=VizDisplayCompositor')

            # Process and memory management
            chrome_options.add_argument('--disable-background-timer-throttling')
            chrome_options.add_argument('--disable-popup-blocking')
            chrome_options.add_argument('--disable-translate')
            chrome_options.add_argument('--disable-default-apps')
            chrome_options.add_argument('--disable-sync')
            chrome_options.add_argument('--disable-background-networking')
            chrome_options.add_argument('--disable-backgrounding-occluded-windows')
            chrome_options.add_argument('--disable-renderer-backgrounding')
            chrome_options.add_argument('--disable-field-trial-config')
            chrome_options.add_argument('--disable-back-forward-cache')
            chrome_options.add_argument('--disable-hang-monitor')
            chrome_options.add_argument('--disable-prompt-on-repost')
            chrome_options.add_argument('--disable-ipc-flooding-protection')

            # Crash and error reporting
            chrome_options.add_argument('--disable-crash-reporter')
            chrome_options.add_argument('--disable-oopr-debug-crash-dump')
            chrome_options.add_argument('--no-crash-upload')
            chrome_options.add_argument('--disable-client-side-phishing-detection')
            chrome_options.add_argument('--disable-component-update')
            chrome_options.add_argument('--disable-domain-reliability')

            # Performance optimizations
            chrome_options.add_argument('--disable-features=TranslateUI')
            chrome_options.add_argument('--disable-features=BlinkGenPropertyTrees')
            chrome_options.add_argument('--disable-low-res-tiling')
            chrome_options.add_argument('--max_old_space_size=4096')
            chrome_options.add_argument('--memory-pressure-off')

            # Logging and debugging
            chrome_options.add_argument('--log-level=3')
            chrome_options.add_argument('--silent')
            chrome_options.add_argument('--disable-logging')
            chrome_options.add_argument('--disable-infobars')

            # Create isolated user data directory
            user_data_dir = tempfile.mkdtemp(prefix='chrome_mcp_')
            chrome_options.add_argument(f'--user-data-dir={user_data_dir}')

            # Set DISPLAY environment variable if not set (for headless)
            if not os.environ.get('DISPLAY'):
                os.environ['DISPLAY'] = ':99'
                logger.info("🖥️ Set DISPLAY=:99 for headless mode")

            logger.info(f"🗂️ Using temporary user data dir: {user_data_dir}")

            # Initialize the Chrome driver with better error handling
            custome_chrome = os.getenv('CUSTOM_CHROME')
            custome_chrome_driver = os.getenv('CUSTOM_CHROME_DRIVER')

            if custome_chrome:
                chrome_options.binary_location = custome_chrome
            if custome_chrome_driver:
                executable_path = custome_chrome_driver
                service = ChromeService(executable_path=executable_path)
            else:
                service = ChromeService()
            driver = webdriver.Chrome(service=service, options=chrome_options)

            # Execute stealth scripts
            driver.execute_cdp_cmd(
                'Page.addScriptToEvaluateOnNewDocument',
                {
                    'source': """
                    Object.defineProperty(navigator, 'webdriver', {
                        get: () => undefined
                    });

                    Object.defineProperty(navigator, 'languages', {
                        get: () => ['en-US', 'en']
                    });

                    Object.defineProperty(navigator, 'plugins', {
                        get: () => [1, 2, 3, 4, 5]
                    });

                    window.chrome = {
                        runtime: {}
                    };

                    Object.defineProperty(navigator, 'permissions', {
                        get: () => ({
                            query: Promise.resolve.bind(Promise)
                        })
                    });
                """
                },
            )
            return driver

        except Exception as e:
            # Clean up any existing driver
            if hasattr(self, 'driver') and self.driver:
                try:
                    self.driver.quit()
                    self.driver = None
                except Exception:
                    pass
            raise

class RVController(Controller):
    def __init__(self, keep_open: bool = False):
        self.browser = RVBrowser(headless=True, keep_open=keep_open)
        self.registry = Registry()
        self._register_default_actions()
        self._register_bing()

    def _register_bing(self):
        """Register all default browser bing actions"""

        # Basic Navigation Actions
        @self.registry.action(
            'Search Bing', param_model=SearchGoogleAction, requires_browser=True
        )
        def search_bing(params: SearchGoogleAction, browser: Browser):
            driver = browser._get_driver()
            driver.get(f'https://www.bing.com/search?q={params.query}')
            browser.wait_for_page_load()

@dataclass
class AppContext:
    controller: Optional[RVController] = None

@asynccontextmanager
async def app_lifespan(server: FastMCP) -> AsyncIterator[AppContext]:
    """Manage application lifecycle with lazy RVController initialization"""
    import sys
    import logging

    # 设置日志
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    logger = logging.getLogger(__name__)

    logger.info("🚀 Starting MCP Search Server...")
    logger.info(f"Python version: {sys.version}")

    app_context = None
    try:
        # Don't initialize browser immediately, do it lazily when needed
        logger.info("✅ MCP Server initialized (browser will be initialized on first use)")

        app_context = AppContext(controller=None)
        yield app_context

    except Exception as e:
        logger.error(f"❌ Failed to initialize MCP Server: {e}")
        raise
    finally:
        # Cleanup on shutdown
        logger.info("🧹 Cleaning up...")
        try:
            if app_context and hasattr(app_context, 'controller') and app_context.controller is not None:
                if hasattr(app_context.controller, 'browser') and app_context.controller.browser.driver:
                    app_context.controller.browser.driver.quit()
                    logger.info("✅ Browser cleanup completed")
        except Exception as e:
            logger.error(f"⚠️ Error during browser cleanup: {e}")
        logger.info("✅ Cleanup completed")

async def _fallback_search(query: str, engine: str = "bing") -> str:
    """Fallback search using simple HTTP requests when browser fails"""
    import aiohttp
    import logging
    import time

    logger = logging.getLogger(__name__)
    logger.info("🔄 Using fallback HTTP search method")

    start_time = time.time()

    try:
        # Construct search URL
        if engine.lower() == "bing":
            search_url = f"https://www.bing.com/search?q={query}"
        elif engine.lower() == "google":
            search_url = f"https://www.google.com/search?q={query}"
        else:
            return f"Error: Unsupported search engine: {engine}"

        # Set up headers to mimic a real browser
        headers = {
            'User-Agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
        }

        async with aiohttp.ClientSession(headers=headers) as session:
            async with session.get(search_url, timeout=30) as response:
                if response.status == 200:
                    content = await response.text()
                    elapsed_time = time.time() - start_time

                    # Simple content extraction (basic)
                    title = "Search Results (Fallback Mode)"

                    return f"""Search Results (Fallback Mode):
Title: {title}
Query: {query}
Engine: {engine}
URL: {search_url}
Time: {elapsed_time:.2f}s
Status: HTTP {response.status}

Note: This is a fallback mode due to browser initialization failure.
Content length: {len(content)} characters

Content Preview:
{content[:800]}...
"""
                else:
                    return f"Error: HTTP {response.status} - {response.reason}"

    except Exception as e:
        elapsed_time = time.time() - start_time
        return f"Fallback search failed after {elapsed_time:.2f}s: {str(e)}"

# Create MCP server with lifespan management
mcp = FastMCP("Browser Search Server", lifespan=app_lifespan)

@mcp.tool()
async def search_web(query: str, engine: str = "bing") -> str:
    """
    Search the web using the specified search engine.

    Args:
        query: The search query to execute
        engine: The search engine to use (bing or google)

    Returns:
        Search results including page title and content
    """
    import logging
    import time
    from selenium.webdriver.common.by import By
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC

    logger = logging.getLogger(__name__)

    if not query:
        return "Error: No query provided"

    logger.info(f"🔍 Starting search: '{query}' on {engine}")
    start_time = time.time()

    try:
        # Get controller from lifespan context (lazy initialization)
        ctx = mcp.get_context()
        app_context = ctx.request_context.lifespan_context

        # Initialize controller if not already done
        if app_context.controller is None:
            logger.info("🌐 Initializing RVController on first use...")
            try:
                app_context.controller = RVController(keep_open=False)
                logger.info("✅ RVController initialized successfully")
            except Exception as e:
                logger.error(f"❌ Failed to initialize RVController: {e}")
                # Fallback to simple HTTP request
                return await _fallback_search(query, engine)

        controller = app_context.controller
        browser = controller.browser.driver

        logger.info("📱 Browser context obtained")

        # Set page load timeout
        browser.set_page_load_timeout(30)  # 30秒超时

        # Perform search based on engine
        search_url = ""
        if engine.lower() == "bing":
            search_url = f"https://www.bing.com/search?q={query}"
        elif engine.lower() == "google":
            search_url = f"https://www.google.com/search?q={query}"
        else:
            return f"Error: Unsupported search engine: {engine}. Use 'bing' or 'google'."

        logger.info(f"🌐 Navigating to: {search_url}")
        browser.get(search_url)

        # Wait for page to load with explicit wait
        wait = WebDriverWait(browser, 10)

        if engine.lower() == "bing":
            # Wait for Bing search results
            try:
                wait.until(EC.presence_of_element_located((By.ID, "b_results")))
                logger.info("✅ Bing search results loaded")
            except:
                logger.warning("⚠️ Bing results container not found, continuing...")
        elif engine.lower() == "google":
            # Wait for Google search results
            try:
                wait.until(EC.presence_of_element_located((By.ID, "search")))
                logger.info("✅ Google search results loaded")
            except:
                logger.warning("⚠️ Google results container not found, continuing...")

        # Extract search results
        page_source = browser.page_source
        title = browser.title
        current_url = browser.current_url

        # Extract text content from search results
        try:
            if engine.lower() == "bing":
                results = browser.find_elements(By.CSS_SELECTOR, ".b_algo h2 a")
                result_texts = [elem.text for elem in results[:5]]  # 前5个结果
            elif engine.lower() == "google":
                results = browser.find_elements(By.CSS_SELECTOR, "h3")
                result_texts = [elem.text for elem in results[:5]]  # 前5个结果
            else:
                result_texts = []

            if result_texts:
                results_summary = "\n".join([f"- {text}" for text in result_texts if text.strip()])
            else:
                results_summary = "No specific results extracted"

        except Exception as e:
            logger.warning(f"⚠️ Could not extract specific results: {e}")
            results_summary = "Results extraction failed"

        elapsed_time = time.time() - start_time
        logger.info(f"✅ Search completed in {elapsed_time:.2f} seconds")

        return f"""Search Results:
Title: {title}
Query: {query}
Engine: {engine}
URL: {current_url}
Time: {elapsed_time:.2f}s

Top Results:
{results_summary}

Page Content Preview:
{page_source[:800]}...
"""

    except Exception as e:
        elapsed_time = time.time() - start_time
        error_msg = f"Error after {elapsed_time:.2f}s: {str(e)}"
        logger.error(f"❌ {error_msg}")
        return error_msg

@mcp.tool()
async def browse_url(url: str) -> str:
    """
    Navigate to a specific URL and extract content

    Args:
        url: The URL to navigate to

    Returns:
        Page content or error message
    """
    import logging
    logger = logging.getLogger(__name__)

    if not url:
        return "Error: No URL provided"

    try:
        # Get controller from lifespan context (lazy initialization)
        ctx = mcp.get_context()
        app_context = ctx.request_context.lifespan_context

        # Initialize controller if not already done
        if app_context.controller is None:
            logger.info("🌐 Initializing RVController on first use...")
            app_context.controller = RVController(keep_open=False)
            logger.info("✅ RVController initialized successfully")

        controller = app_context.controller
        browser = controller.browser.driver

        # Navigate to URL
        browser.get(url)

        # Wait for page to load
        await asyncio.sleep(2)

        # Extract page content
        page_source = browser.page_source
        title = browser.title
        current_url = browser.current_url

        return f"Title: {title}\nURL: {current_url}\nContent: {page_source[:1000]}..."

    except Exception as e:
        return f"Error: {str(e)}"

@mcp.resource("search://results/{query}")
def get_search_results(query: str) -> str:
    """Get cached search results for a query"""
    return f"Search results resource for query: {query}"

@mcp.prompt()
def search_prompt(query: str) -> str:
    """Create a search prompt template"""
    return f"Please search for information about: {query}"

async def test_search_direct():
    """Direct test function without MCP context"""
    import logging
    import time
    from selenium.webdriver.common.by import By
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC

    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    logger = logging.getLogger(__name__)

    query = "搜索中国最美十大景区，从南到北输出。"
    engine = "bing"

    logger.info(f"🧪 Starting direct test...")
    logger.info(f"🔍 Testing search: '{query}' on {engine}")
    start_time = time.time()

    try:
        # Create controller directly for testing
        controller = RVController(keep_open=False)
        browser = controller.browser.driver

        logger.info("📱 Browser initialized for testing")

        # Set page load timeout
        browser.set_page_load_timeout(30)

        # Perform search
        search_url = f"https://www.bing.com/search?q={query}"
        logger.info(f"🌐 Navigating to: {search_url}")
        browser.get(search_url)

        # Wait for page to load
        wait = WebDriverWait(browser, 10)
        try:
            wait.until(EC.presence_of_element_located((By.ID, "b_results")))
            logger.info("✅ Bing search results loaded")
        except:
            logger.warning("⚠️ Bing results container not found, continuing...")

        # Extract search results
        page_source = browser.page_source
        title = browser.title
        current_url = browser.current_url

        # Extract text content from search results
        try:
            results = browser.find_elements(By.CSS_SELECTOR, ".b_algo h2 a")
            result_texts = [elem.text for elem in results[:5]]

            if result_texts:
                results_summary = "\n".join([f"- {text}" for text in result_texts if text.strip()])
            else:
                results_summary = "No specific results extracted"

        except Exception as e:
            logger.warning(f"⚠️ Could not extract specific results: {e}")
            results_summary = "Results extraction failed"

        elapsed_time = time.time() - start_time
        logger.info(f"✅ Search completed in {elapsed_time:.2f} seconds")

        result = f"""Search Results:
Title: {title}
Query: {query}
Engine: {engine}
URL: {current_url}
Time: {elapsed_time:.2f}s

Top Results:
{results_summary}

Page Content Preview:
{page_source[:800]}...
"""

        print("=" * 50)
        print("SEARCH RESULTS:")
        print("=" * 50)
        print(result)

        return result

    except Exception as e:
        elapsed_time = time.time() - start_time
        error_msg = f"Error after {elapsed_time:.2f}s: {str(e)}"
        logger.error(f"❌ {error_msg}")
        print(f"Test failed: {error_msg}")
        return error_msg
    finally:
        # Cleanup
        try:
            if 'controller' in locals() and controller.browser.driver:
                controller.browser.driver.quit()
                logger.info("✅ Browser cleanup completed")
        except Exception as e:
            logger.error(f"⚠️ Error during cleanup: {e}")

async def test_tool_functionality(mcp_server):
    # Pass the server directly to the Client constructor
    async with Client(mcp_server) as client:
        result = await client.call_tool("search_web", {"query": "搜索中国最美十大景区，从南到北输出。"})
        print("###### test_tool_functionality output: "+ result[0].text)

if __name__ == "__main__":
    import sys
    if len(sys.argv) > 1 and sys.argv[1] == "direct":
        # Test mode
        print("Starting test mode...")
        asyncio.run(test_search_direct())
    elif len(sys.argv) > 1 and sys.argv[1] == "test":
        # Test mode
        print("Starting test mode...")
        asyncio.run(test_tool_functionality(mcp))
    else:
        # Normal MCP server mode
        print("Starting MCP Search Server with stdio protocol...")
        mcp.run()
