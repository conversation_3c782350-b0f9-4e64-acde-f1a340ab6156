#!/usr/bin/env python3
"""
Search MCP Server
Provides web search capabilities through MCP protocol using stdio
"""

import asyncio
import os
from typing import Optional
from dotenv import load_dotenv
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service as ChromeService
from contextlib import asynccontextmanager
from typing import AsyncIterator
from dataclasses import dataclass

# Import official MCP SDK
from mcp.server.fastmcp import FastMCP

load_dotenv()

# Configuration
CUSTOM_CHROME = os.getenv('CUSTOM_CHROME')
CUSTOM_CHROME_DRIVER = os.getenv('CUSTOM_CHROME_DRIVER')

@dataclass
class AppContext:
    browser: webdriver.Chrome

@asynccontextmanager
async def app_lifespan(server: FastMCP) -> AsyncIterator[AppContext]:
    """Manage application lifecycle with browser setup and cleanup"""
    import sys
    import logging

    # 设置日志
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    logger = logging.getLogger(__name__)

    logger.info("🚀 Starting MCP Search Server...")
    logger.info(f"Python version: {sys.version}")

    try:
        # Initialize browser on startup
        logger.info("🌐 Initializing Chrome browser...")
        # if webdriver is not starting, try to kill it or rm -rf ~/.wdm
        chrome_options = Options()
        chrome_options.add_argument('--headless=new')  # Updated headless argument

        # Essential automation and performance settings
        chrome_options.add_argument('--disable-blink-features=AutomationControlled')
        chrome_options.add_experimental_option('excludeSwitches', ['enable-automation'])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--window-size=1280,1024')
        chrome_options.add_argument('--disable-extensions')

        # Background process optimization
        chrome_options.add_argument('--disable-background-timer-throttling')
        chrome_options.add_argument('--disable-popup-blocking')

        # Additional stealth settings
        chrome_options.add_argument('--disable-infobars')
        # Much better when working in non-headless mode
        chrome_options.add_argument('--disable-backgrounding-occluded-windows')
        chrome_options.add_argument('--disable-renderer-backgrounding')

        if CUSTOM_CHROME:
            chrome_options.binary_location = CUSTOM_CHROME
            logger.info(f"Using custom Chrome: {CUSTOM_CHROME}")

        if CUSTOM_CHROME_DRIVER:
            service = ChromeService(executable_path=CUSTOM_CHROME_DRIVER)
            logger.info(f"Using custom ChromeDriver: {CUSTOM_CHROME_DRIVER}")
        else:
            service = ChromeService()
            logger.info("Using system ChromeDriver")

        # 设置服务超时
        service.start_error_message = "Chrome failed to start"

        logger.info("Creating Chrome WebDriver instance...")
        browser = webdriver.Chrome(service=service, options=chrome_options)
        logger.info("✅ Chrome browser initialized successfully")

            # Execute stealth scripts
        browser.execute_cdp_cmd(
            'Page.addScriptToEvaluateOnNewDocument',
            {
                'source': """
                Object.defineProperty(navigator, 'webdriver', {
                    get: () => undefined
                });
                
                Object.defineProperty(navigator, 'languages', {
                    get: () => ['en-US', 'en']
                });
                
                Object.defineProperty(navigator, 'plugins', {
                    get: () => [1, 2, 3, 4, 5]
                });
                
                window.chrome = {
                    runtime: {}
                };
                
                Object.defineProperty(navigator, 'permissions', {
                    get: () => ({
                        query: Promise.resolve.bind(Promise)
                    })
                });
            """
            },
        )
        
        # 测试浏览器是否工作
        logger.info("🧪 Testing browser functionality...")
        browser.get("data:text/html,<html><body><h1>MCP Server Ready</h1></body></html>")
        logger.info("✅ Browser test successful")

        yield AppContext(browser=browser)

    except Exception as e:
        logger.error(f"❌ Failed to initialize browser: {e}")
        raise
    finally:
        # Cleanup on shutdown
        logger.info("🧹 Cleaning up browser...")
        try:
            if 'browser' in locals():
                browser.quit()
                logger.info("✅ Browser cleanup completed")
        except Exception as e:
            logger.error(f"⚠️ Error during cleanup: {e}")

# Create MCP server with lifespan management
mcp = FastMCP("Browser Search Server", lifespan=app_lifespan)

@mcp.tool()
async def search_web(query: str, engine: str = "bing") -> str:
    """
    Search the web using the specified search engine.

    Args:
        query: The search query to execute
        engine: The search engine to use (bing or google)

    Returns:
        Search results including page title and content
    """
    import logging
    import time
    from selenium.webdriver.common.by import By
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC

    logger = logging.getLogger(__name__)

    if not query:
        return "Error: No query provided"

    logger.info(f"🔍 Starting search: '{query}' on {engine}")
    start_time = time.time()

    try:
        # Get browser from lifespan context
        ctx = mcp.get_context()
        browser = ctx.request_context.lifespan_context.browser

        logger.info("📱 Browser context obtained")

        # Set page load timeout
        browser.set_page_load_timeout(30)  # 30秒超时

        # Perform search based on engine
        search_url = ""
        if engine.lower() == "bing":
            search_url = f"https://www.bing.com/search?q={query}"
        elif engine.lower() == "google":
            search_url = f"https://www.google.com/search?q={query}"
        else:
            return f"Error: Unsupported search engine: {engine}. Use 'bing' or 'google'."

        logger.info(f"🌐 Navigating to: {search_url}")
        browser.get(search_url)

        # Wait for page to load with explicit wait
        wait = WebDriverWait(browser, 10)

        if engine.lower() == "bing":
            # Wait for Bing search results
            try:
                wait.until(EC.presence_of_element_located((By.ID, "b_results")))
                logger.info("✅ Bing search results loaded")
            except:
                logger.warning("⚠️ Bing results container not found, continuing...")
        elif engine.lower() == "google":
            # Wait for Google search results
            try:
                wait.until(EC.presence_of_element_located((By.ID, "search")))
                logger.info("✅ Google search results loaded")
            except:
                logger.warning("⚠️ Google results container not found, continuing...")

        # Extract search results
        page_source = browser.page_source
        title = browser.title
        current_url = browser.current_url

        # Extract text content from search results
        try:
            if engine.lower() == "bing":
                results = browser.find_elements(By.CSS_SELECTOR, ".b_algo h2 a")
                result_texts = [elem.text for elem in results[:5]]  # 前5个结果
            elif engine.lower() == "google":
                results = browser.find_elements(By.CSS_SELECTOR, "h3")
                result_texts = [elem.text for elem in results[:5]]  # 前5个结果
            else:
                result_texts = []

            if result_texts:
                results_summary = "\n".join([f"- {text}" for text in result_texts if text.strip()])
            else:
                results_summary = "No specific results extracted"

        except Exception as e:
            logger.warning(f"⚠️ Could not extract specific results: {e}")
            results_summary = "Results extraction failed"

        elapsed_time = time.time() - start_time
        logger.info(f"✅ Search completed in {elapsed_time:.2f} seconds")

        return f"""Search Results:
Title: {title}
Query: {query}
Engine: {engine}
URL: {current_url}
Time: {elapsed_time:.2f}s

Top Results:
{results_summary}

Page Content Preview:
{page_source[:800]}...
"""

    except Exception as e:
        elapsed_time = time.time() - start_time
        error_msg = f"Error after {elapsed_time:.2f}s: {str(e)}"
        logger.error(f"❌ {error_msg}")
        return error_msg

@mcp.tool()
async def browse_url(url: str) -> str:
    """
    Navigate to a specific URL and extract content

    Args:
        url: The URL to navigate to

    Returns:
        Page content or error message
    """
    if not url:
        return "Error: No URL provided"

    try:
        # Get browser from lifespan context
        ctx = mcp.get_context()
        browser = ctx.request_context.lifespan_context.browser

        # Navigate to URL
        browser.get(url)

        # Wait for page to load
        await asyncio.sleep(2)

        # Extract page content
        page_source = browser.page_source
        title = browser.title
        current_url = browser.current_url

        return f"Title: {title}\nURL: {current_url}\nContent: {page_source[:1000]}..."

    except Exception as e:
        return f"Error: {str(e)}"

@mcp.resource("search://results/{query}")
def get_search_results(query: str) -> str:
    """Get cached search results for a query"""
    return f"Search results resource for query: {query}"

@mcp.prompt()
def search_prompt(query: str) -> str:
    """Create a search prompt template"""
    return f"Please search for information about: {query}"

if __name__ == "__main__":
    print("Starting MCP Search Server with stdio protocol...")
    mcp.run()
