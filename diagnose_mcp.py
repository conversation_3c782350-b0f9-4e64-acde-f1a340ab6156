#!/usr/bin/env python3
"""
MCP 服务器诊断脚本
帮助排查超时和连接问题
"""

import subprocess
import sys
import time
import json
import os
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def check_dependencies():
    """检查依赖是否正确安装"""
    logger.info("🔍 检查依赖...")
    
    deps = {
        'mcp': 'MCP SDK',
        'selenium': 'Selenium WebDriver',
        'dotenv': 'Python-dotenv'
    }
    
    missing = []
    for dep, name in deps.items():
        try:
            __import__(dep)
            logger.info(f"✅ {name} 已安装")
        except ImportError:
            logger.error(f"❌ {name} 未安装")
            missing.append(dep)
    
    return len(missing) == 0


def check_chrome():
    """检查 Chrome 和 ChromeDriver"""
    logger.info("🌐 检查 Chrome 浏览器...")
    
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        
        options = Options()
        options.add_argument('--headless')
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--disable-gpu')
        
        logger.info("启动 Chrome 测试...")
        start_time = time.time()
        
        driver = webdriver.Chrome(options=options)
        
        # 测试基本功能
        driver.get("data:text/html,<html><body><h1>Test</h1></body></html>")
        title = driver.title
        
        driver.quit()
        
        elapsed = time.time() - start_time
        logger.info(f"✅ Chrome 测试成功，耗时 {elapsed:.2f} 秒")
        
        if elapsed > 10:
            logger.warning(f"⚠️ Chrome 启动较慢 ({elapsed:.2f}s)，可能导致超时")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Chrome 测试失败: {e}")
        return False


def test_mcp_server_startup():
    """测试 MCP 服务器启动"""
    logger.info("🚀 测试 MCP 服务器启动...")
    
    try:
        # 启动服务器进程
        process = subprocess.Popen(
            [sys.executable, "mcp_server.py"],
            stdin=subprocess.PIPE,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        logger.info("等待服务器启动...")
        time.sleep(5)  # 给服务器更多启动时间
        
        if process.poll() is not None:
            stdout, stderr = process.communicate()
            logger.error(f"❌ 服务器启动失败")
            logger.error(f"STDOUT: {stdout}")
            logger.error(f"STDERR: {stderr}")
            return False
        
        logger.info("✅ 服务器启动成功")
        
        # 发送初始化消息
        init_msg = {
            "jsonrpc": "2.0",
            "id": 1,
            "method": "initialize",
            "params": {
                "protocolVersion": "2024-11-05",
                "capabilities": {},
                "clientInfo": {"name": "diagnostic", "version": "1.0"}
            }
        }
        
        logger.info("发送初始化消息...")
        process.stdin.write(json.dumps(init_msg) + "\n")
        process.stdin.flush()
        
        # 等待响应
        time.sleep(2)
        
        if process.poll() is not None:
            stdout, stderr = process.communicate()
            logger.error(f"❌ 服务器在初始化后崩溃")
            logger.error(f"STDOUT: {stdout}")
            logger.error(f"STDERR: {stderr}")
            return False
        
        logger.info("✅ 初始化消息发送成功")
        
        # 清理
        process.terminate()
        process.wait(timeout=5)
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 服务器测试失败: {e}")
        return False


def test_network():
    """测试网络连接"""
    logger.info("🌍 测试网络连接...")
    
    import urllib.request
    
    test_urls = [
        "https://www.bing.com",
        "https://www.google.com",
        "https://httpbin.org/get"
    ]
    
    for url in test_urls:
        try:
            start_time = time.time()
            response = urllib.request.urlopen(url, timeout=10)
            elapsed = time.time() - start_time
            
            if response.status == 200:
                logger.info(f"✅ {url} 连接成功 ({elapsed:.2f}s)")
            else:
                logger.warning(f"⚠️ {url} 返回状态码 {response.status}")
                
        except Exception as e:
            logger.error(f"❌ {url} 连接失败: {e}")


def generate_optimized_config():
    """生成优化的配置建议"""
    logger.info("📝 生成优化配置建议...")
    
    config = {
        "mcpServers": {
            "browser_search": {
                "command": "/bin/bash",
                "args": [
                    "--noprofile",
                    "--norc",
                    "-c",
                    "source /home/<USER>/src/browser-use-demo/venv/bin/activate && python /home/<USER>/src/browser-use-demo/mcp_server.py"
                ],
                "timeout": 120,  # 增加到2分钟
                "env": {
                    "PYTHONUNBUFFERED": "1",  # 确保日志实时输出
                    "DISPLAY": ":0"  # 如果需要显示
                }
            }
        }
    }
    
    with open("optimized_config.json", "w") as f:
        json.dump(config, f, indent=2)
    
    logger.info("✅ 优化配置已保存到 optimized_config.json")
    
    print("\n" + "="*50)
    print("📋 配置建议:")
    print("="*50)
    print("1. 增加超时时间到 120 秒")
    print("2. 设置环境变量 PYTHONUNBUFFERED=1")
    print("3. 确保虚拟环境路径正确")
    print("4. 检查 Chrome 和 ChromeDriver 版本兼容性")
    print()


def main():
    """主诊断函数"""
    print("🔧 MCP 服务器诊断工具")
    print("="*40)
    
    # 检查当前目录
    if not os.path.exists("mcp_server.py"):
        logger.error("❌ 未找到 mcp_server.py 文件")
        return
    
    # 运行诊断
    deps_ok = check_dependencies()
    chrome_ok = check_chrome()
    network_ok = test_network()
    
    if deps_ok and chrome_ok:
        server_ok = test_mcp_server_startup()
    else:
        server_ok = False
    
    # 生成报告
    print("\n" + "="*50)
    print("📊 诊断报告:")
    print("="*50)
    print(f"依赖检查: {'✅ 通过' if deps_ok else '❌ 失败'}")
    print(f"Chrome 检查: {'✅ 通过' if chrome_ok else '❌ 失败'}")
    print(f"网络检查: {'✅ 通过' if network_ok else '❌ 失败'}")
    print(f"服务器启动: {'✅ 通过' if server_ok else '❌ 失败'}")
    
    if not all([deps_ok, chrome_ok, server_ok]):
        print("\n🔧 建议的解决方案:")
        
        if not deps_ok:
            print("- 安装缺失的依赖: pip install mcp selenium python-dotenv")
        
        if not chrome_ok:
            print("- 安装 Chrome 浏览器")
            print("- 更新 ChromeDriver: https://chromedriver.chromium.org/")
            print("- 检查 Chrome 和 ChromeDriver 版本兼容性")
        
        if not server_ok:
            print("- 检查服务器日志输出")
            print("- 增加启动超时时间")
            print("- 确保虚拟环境配置正确")
    
    # 生成优化配置
    generate_optimized_config()
    
    print("\n📝 下一步:")
    print("1. 修复上述问题")
    print("2. 使用 optimized_config.json 中的配置")
    print("3. 重新测试 MCP 连接")


if __name__ == "__main__":
    main()
