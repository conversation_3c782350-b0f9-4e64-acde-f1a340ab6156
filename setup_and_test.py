#!/usr/bin/env python3
"""
MCP 搜索服务器安装和测试脚本
自动安装依赖并运行测试
"""

import subprocess
import sys
import os
import asyncio


def install_dependencies():
    """安装必要的依赖"""
    print("📦 安装依赖包...")
    
    dependencies = [
        "mcp",
        "selenium", 
        "python-dotenv"
    ]
    
    for dep in dependencies:
        print(f"安装 {dep}...")
        try:
            subprocess.check_call([
                sys.executable, "-m", "pip", "install", dep
            ], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
            print(f"✅ {dep} 安装成功")
        except subprocess.CalledProcessError:
            print(f"❌ {dep} 安装失败")
            return False
    
    return True


def check_chrome():
    """检查 Chrome 浏览器是否可用"""
    print("🌐 检查 Chrome 浏览器...")
    
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        
        options = Options()
        options.add_argument('--headless')
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        
        driver = webdriver.Chrome(options=options)
        driver.quit()
        print("✅ Chrome 浏览器可用")
        return True
        
    except Exception as e:
        print(f"❌ Chrome 浏览器不可用: {e}")
        print("请安装 Chrome 浏览器和 ChromeDriver")
        return False


async def run_basic_test():
    """运行基本测试"""
    print("🧪 运行基本测试...")
    
    try:
        # 导入测试模块
        sys.path.insert(0, os.path.dirname(__file__))
        import simple_test
        
        # 运行测试
        success = await simple_test.test_server_basic()
        if success:
            print("✅ 基本测试通过")
            return True
        else:
            print("❌ 基本测试失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试运行失败: {e}")
        return False


def create_example_env():
    """创建示例环境配置文件"""
    env_content = """# MCP 搜索服务器配置
# 可选：自定义 Chrome 浏览器路径
# CUSTOM_CHROME=/path/to/chrome

# 可选：自定义 ChromeDriver 路径  
# CUSTOM_CHROME_DRIVER=/path/to/chromedriver
"""
    
    if not os.path.exists(".env"):
        with open(".env", "w", encoding="utf-8") as f:
            f.write(env_content)
        print("✅ 创建了示例 .env 配置文件")
    else:
        print("⚠️  .env 文件已存在，跳过创建")


def show_usage_instructions():
    """显示使用说明"""
    print("\n" + "="*50)
    print("🎉 安装和测试完成!")
    print("="*50)
    print()
    print("📖 使用方法:")
    print()
    print("1. 直接运行服务器:")
    print("   python mcp_server.py")
    print()
    print("2. 运行客户端测试:")
    print("   python mcp_client_example.py")
    print()
    print("3. 交互式客户端:")
    print("   python mcp_client_example.py --interactive")
    print()
    print("4. 运行完整测试:")
    print("   python simple_test.py")
    print()
    print("📋 在 Claude Desktop 中配置:")
    print('   在配置文件中添加:')
    print('   {')
    print('     "mcpServers": {')
    print('       "search-server": {')
    print('         "command": "python",')
    print(f'         "args": ["{os.path.abspath("mcp_server.py")}"]')
    print('       }')
    print('     }')
    print('   }')
    print()
    print("🔧 故障排除:")
    print("   - 确保 Chrome 浏览器已安装")
    print("   - 检查网络连接")
    print("   - 查看 README.md 获取详细说明")


async def main():
    """主函数"""
    print("🚀 MCP 搜索服务器安装和测试")
    print("="*40)
    print()
    
    # 检查 Python 版本
    if sys.version_info < (3, 8):
        print("❌ 需要 Python 3.8 或更高版本")
        return
    
    print(f"✅ Python 版本: {sys.version}")
    print()
    
    # 安装依赖
    if not install_dependencies():
        print("❌ 依赖安装失败，请手动安装")
        return
    
    print()
    
    # 检查 Chrome
    if not check_chrome():
        print("❌ Chrome 检查失败，请安装 Chrome 和 ChromeDriver")
        print("   下载地址: https://chromedriver.chromium.org/")
        return
    
    print()
    
    # 创建配置文件
    create_example_env()
    print()
    
    # 运行测试
    test_success = await run_basic_test()
    print()
    
    if test_success:
        show_usage_instructions()
    else:
        print("❌ 测试失败，请检查配置")
        print("查看 README.md 获取故障排除信息")


if __name__ == "__main__":
    asyncio.run(main())
